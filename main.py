import concurrent.futures
import argparse
import threading
import os
import queue
from utils.downloader import download_video
from utils.uploader import upload_video
from config import UPLOAD_TO_S3, UPLOAD_TO_TOS, MAX_WORKERS, DATA_DIR

# Create a lock to prevent race conditions when writing to files
file_lock = threading.Lock()

def get_processed_ids_from_log(file_path):
    """
    Reads a log file where each line is expected to be a video ID.
    Returns a set of unique video IDs.
    """
    print(f"INFO: Reading processed IDs from: {file_path}")
    if not os.path.exists(file_path):
        print(f"INFO: Log file not found: {file_path}")
        return set()
    with open(file_path, 'r', encoding='utf-8') as f:
        processed_ids = {line.strip() for line in f if line.strip()}
    print(f"INFO: Loaded {len(processed_ids)} unique IDs from {os.path.basename(file_path)}.")
    return processed_ids

def resume_interrupted_uploads(data_dir, upload_queue):
    """
    Finds downloaded but not uploaded files and queues them for upload.
    """
    print("INFO: Checking for interrupted uploads...")
    processed_log_path = os.path.join(data_dir, "processed_video.txt")
    downloads_dir = os.path.join(data_dir, "downloads")

    uploaded_video_ids = get_processed_ids_from_log(processed_log_path)

    if not os.path.exists(downloads_dir):
        print("INFO: Downloads directory does not exist. No interrupted uploads to resume.")
        return

    resumed_count = 0
    for filename in os.listdir(downloads_dir):
        if filename.endswith(('.mp4', '.mkv', '.webm')):
            local_video_id = filename.split('_')[0]
            
            if local_video_id not in uploaded_video_ids:
                print(f"INFO: Found interrupted upload: {filename}. Queueing for upload.")
                file_path = os.path.join(downloads_dir, filename)
                upload_queue.put(file_path)
                resumed_count += 1
    
    if resumed_count == 0:
        print("INFO: No interrupted uploads found.")
    else:
        print(f"INFO: Queued {resumed_count} interrupted uploads for processing.")
    print("INFO: Finished checking for interrupted uploads.")

def download_worker(video_url, upload_queue, data_dir, cookies_file, file_lock):
    """
    Downloads a video and puts its path into the upload queue.
    """
    print(f"INFO: [Downloader] Starting to process video: {video_url}")
    downloaded_file_path = download_video(video_url, file_lock, data_dir, cookies_file)
    if downloaded_file_path:
        print(f"INFO: [Downloader] Video downloaded: {os.path.basename(downloaded_file_path)}. Queueing for upload.")
        upload_queue.put(downloaded_file_path)
    else:
        print(f"WARN: [Downloader] Download failed for {video_url}, skipping upload.")

def upload_worker(upload_queue, file_lock, data_dir):
    """
    Takes a file path from the queue and uploads it.
    """
    while True:
        file_path = upload_queue.get()
        if file_path is None:
            break
        
        print(f"INFO: [Uploader] Dequeued {os.path.basename(file_path)} for upload.")
        try:
            upload_video(file_path, file_lock, data_dir)
        except Exception as e:
            print(f"ERROR: [Uploader] Exception during upload of {os.path.basename(file_path)}: {e}")
        finally:
            upload_queue.task_done()

if __name__ == "__main__":
    print("INFO: Application starting...")
    parser = argparse.ArgumentParser(description="Download and upload videos from a list of URLs.")
    parser.add_argument('-f', '--file', default='video_urls.txt', help='Path to the file containing video URLs.')
    parser.add_argument('-d', '--data-dir', default=DATA_DIR, help='Directory to store downloaded videos and logs.')
    parser.add_argument('--cookies', help='Path to the cookies file.')
    args = parser.parse_args()
    print(f"INFO: Arguments: file='{args.file}', data-dir='{args.data_dir}', cookies='{args.cookies}'")

    data_dir = args.data_dir
    if not os.path.exists(data_dir):
        print(f"INFO: Creating data directory: {data_dir}")
        os.makedirs(data_dir)
    downloads_path = os.path.join(data_dir, 'downloads')
    if not os.path.exists(downloads_path):
        print(f"INFO: Creating downloads directory: {downloads_path}")
        os.makedirs(downloads_path)

    upload_queue = queue.Queue()

    if UPLOAD_TO_S3 or UPLOAD_TO_TOS:
        resume_interrupted_uploads(args.data_dir, upload_queue)

    print("INFO: Loading processed video IDs to avoid re-downloading.")
    processed_downloaded = get_processed_ids_from_log(os.path.join(data_dir, 'downloaded_video.txt'))
    processed_uploaded = get_processed_ids_from_log(os.path.join(data_dir, 'processed_video.txt'))
    processed_ids = processed_downloaded.union(processed_uploaded)
    print(f"INFO: Total unique processed video IDs loaded: {len(processed_ids)}")

    try:
        print(f"INFO: Reading video URLs from: {args.file}")
        with open(args.file, 'r') as f:
            video_urls = []
            total_urls_in_file = 0
            skipped_count = 0
            for line in f:
                total_urls_in_file += 1
                line = line.strip()
                if not line:
                    continue
                
                video_id = line
                if '=' in line:
                    video_id = line.split('=')[-1]
                if '&' in video_id:
                    video_id = video_id.split('&')[0]

                if video_id in processed_ids:
                    skipped_count += 1
                    continue

                if line.startswith('http'):
                    video_urls.append(line)
                else:
                    video_urls.append(f"https://www.youtube.com/watch?v={line}")
        
        print(f"INFO: Found {total_urls_in_file} URLs in file. Skipped {skipped_count} already processed. Adding {len(video_urls)} new videos to the queue.")

    except FileNotFoundError:
        print(f"ERROR: The file '{args.file}' was not found.")
        exit(1)

    if not video_urls and upload_queue.empty():
        print("INFO: No new videos to process and no interrupted uploads to resume. Exiting.")
    else:
        download_worker_count = MAX_WORKERS
        upload_worker_count = MAX_WORKERS
        print(f"INFO: Starting pipeline with {download_worker_count} download workers and {upload_worker_count} upload workers.")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=upload_worker_count, thread_name_prefix='Uploader') as upload_executor, \
             concurrent.futures.ThreadPoolExecutor(max_workers=download_worker_count, thread_name_prefix='Downloader') as download_executor:

            uploader_futures = [upload_executor.submit(upload_worker, upload_queue, file_lock, data_dir) for _ in range(upload_worker_count)]

            if video_urls:
                download_futures = [download_executor.submit(download_worker, url, upload_queue, data_dir, args.cookies, file_lock) for url in video_urls]
                
                for future in concurrent.futures.as_completed(download_futures):
                    try:
                        future.result()
                    except Exception as exc:
                        print(f'ERROR: A download task generated an exception: {exc}')
            
            print("INFO: All download tasks are complete.")
            print("INFO: Waiting for all pending uploads to complete...")
            upload_queue.join()
            print("INFO: Upload queue is empty.")

            print("INFO: Sending stop signals to upload workers.")
            for _ in range(upload_worker_count):
                upload_queue.put(None)
            
            concurrent.futures.wait(uploader_futures)
            print("INFO: All uploader workers have shut down.")

    print("INFO: Application finished.")