import os
import time
import yt_dlp
from config import generate_proxy_url, DOWNLOAD_MAX_RETRIES, DOWNLOAD_RETRY_DELAY_SECONDS

def download_video(video_url, file_lock, data_dir, cookies_file=None):
    """
    Downloads a video from a given URL with a retry mechanism.
    """
    for attempt in range(DOWNLOAD_MAX_RETRIES):
        try:
            PROXY_URL, PROXY_INFO = generate_proxy_url()
            proxy_display_name = "None"
            if PROXY_URL and PROXY_INFO:
                proxy_display_name = PROXY_INFO.get('name', f"{PROXY_INFO['user']}@{PROXY_INFO['host']}")
            print(f"Attempt {attempt + 1}/{DOWNLOAD_MAX_RETRIES}: Downloading '{video_url}' with proxy: {proxy_display_name}")

            """
                --min-split-size is -k
                --max-connection-per-server is -x
                --max-concurrent-downloads is -j
                --split is -s
            """
            ydl_opts = {
                'format': 'best[ext=mp4][height=480]/bestvideo[ext=mp4][height=480]+bestaudio[ext=m4a]',
                'writeinfojson': True,
                'addmetadata': True,
                'merge_output_format': 'mp4',
                'proxy': PROXY_URL,
                'socket_timeout': 360,
                'downloader': 'aria2c',
                'downloader_args': 'aria2c:-c -j 10 -s 10 -x 10 -k 2M',
                'outtmpl': os.path.join(data_dir, 'downloads', '%(id)s_audio_video.%(ext)s'),
                'http_chunk_size': 10 * 1024 * 1024,
                'retries': 51,
                'file_access_retries': 52,
                'fragment_retries': 53,
                'keepfragments': False,
                'keepvideo':True,
                "concurrent_fragments": 10
            }

            if cookies_file:
                ydl_opts['cookies'] = cookies_file
                print(f"INFO: Using cookies from file: {cookies_file}")

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info_dict = ydl.extract_info(video_url, download=True)
                file_path = ydl.prepare_filename(info_dict)
                video_id = info_dict['id']
                print(f"Downloaded '{info_dict['title']}' successfully. Video ID: {video_id}")
                
                with file_lock:
                    with open(os.path.join(data_dir, "downloaded_video.txt"), "a") as f:
                        f.write(f"{video_id}\n")
                return file_path  # Success, exit the loop and function

        except Exception as e:
            print(f"ERROR: Attempt {attempt + 1}/{DOWNLOAD_MAX_RETRIES} failed for {video_url}: {e}")
            if attempt < DOWNLOAD_MAX_RETRIES - 1:
                print(f"INFO: Retrying in {DOWNLOAD_RETRY_DELAY_SECONDS} seconds...")
                time.sleep(DOWNLOAD_RETRY_DELAY_SECONDS)
            else:
                print(f"ERROR: All {DOWNLOAD_MAX_RETRIES} attempts failed for {video_url}. Giving up.")
    
    return None # All retries failed