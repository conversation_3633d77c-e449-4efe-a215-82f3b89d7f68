import os
import boto3
import requests
import json
import argparse
import tos
from datetime import datetime, timedelta, timezone
from botocore.client import Config
from botocore.exceptions import ClientError
from config import (
    S3_ENDPOINT_URL,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    S3_BUCKET,
    S3_PREFIX,
    LARK_WEBHOOK_URL,
    BOTO_CONNECT_TIMEOUT,
    BOTO_READ_TIMEOUT,
    TOS_ENDPOINT,
    TOS_REGION,
    TOS_ACCESS_KEY,
    TOS_SECRET_KEY,
    TOS_BUCKET_NAME,
    TOS_PREFIX
)

def get_s3_file_stats():
    """
    Fetches file statistics from an S3-compatible service like Cloudflare R2.
    """
    if not all([S3_ENDPOINT_URL, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, S3_BUCKET]):
        print("S3 credentials are not fully configured.")
        return None

    try:
        s3_client = boto3.client(
            's3',
            endpoint_url=S3_ENDPOINT_URL,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            config=Config(
                connect_timeout=BOTO_CONNECT_TIMEOUT,
                read_timeout=BOTO_READ_TIMEOUT,
                signature_version='s3v4'
            )
        )

        total_files = 0
        total_size = 0
        latest_upload_time = None
        last_hour_files = 0
        last_hour_size = 0

        now = datetime.now(timezone.utc)
        one_hour_ago = now - timedelta(hours=1)

        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=S3_BUCKET, Prefix=S3_PREFIX)

        for page in pages:
            if 'Contents' in page:
                for obj in page['Contents']:
                    total_files += 1
                    total_size += obj['Size']
                    
                    last_modified = obj['LastModified'].astimezone(timezone.utc)

                    if latest_upload_time is None or last_modified > latest_upload_time:
                        latest_upload_time = last_modified

                    if last_modified > one_hour_ago:
                        last_hour_files += 1
                        last_hour_size += obj['Size']
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "latest_upload_time": latest_upload_time,
            "last_hour_files": last_hour_files,
            "last_hour_size": last_hour_size,
        }

    except ClientError as e:
        print(f"Error fetching stats from S3. Boto3 ClientError: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while fetching stats from S3: {e}")
        return None

def get_tos_file_stats():
    """
    Fetches file statistics from Volcengine TOS.
    """
    if not all([TOS_ENDPOINT, TOS_REGION, TOS_ACCESS_KEY, TOS_SECRET_KEY, TOS_BUCKET_NAME]):
        print("TOS credentials are not fully configured.")
        return None

    try:
        client = tos.TosClientV2(
            ak=TOS_ACCESS_KEY,
            sk=TOS_SECRET_KEY,
            endpoint=TOS_ENDPOINT,
            region=TOS_REGION
        )

        total_files = 0
        total_size = 0
        latest_upload_time = None
        last_hour_files = 0
        last_hour_size = 0

        now = datetime.now(timezone.utc)
        one_hour_ago = now - timedelta(hours=1)
        
        marker = ''
        while True:
            result = client.list_objects(bucket=TOS_BUCKET_NAME, prefix=TOS_PREFIX, marker=marker)
            if result.status_code != 200:
                print(f"Failed to list TOS objects. Status: {result.status_code}, Request ID: {result.request_id}")
                break

            for content in result.contents:
                total_files += 1
                total_size += content.size
                
                last_modified_str = content.last_modified
                last_modified = datetime.strptime(last_modified_str, '%Y-%m-%dT%H:%M:%S.%fZ').replace(tzinfo=timezone.utc)

                if latest_upload_time is None or last_modified > latest_upload_time:
                    latest_upload_time = last_modified

                if last_modified > one_hour_ago:
                    last_hour_files += 1
                    last_hour_size += content.size
            
            if result.is_truncated:
                marker = result.next_marker
            else:
                break
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "latest_upload_time": latest_upload_time,
            "last_hour_files": last_hour_files,
            "last_hour_size": last_hour_size,
        }

    except Exception as e:
        print(f"An unexpected error occurred while fetching stats from TOS: {e}")
        return None

def format_size(size_bytes):
    """
    Formats size in bytes to a human-readable format (KB, MB, GB).
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024**2:
        return f"{size_bytes/1024:.2f} KB"
    elif size_bytes < 1024**3:
        return f"{size_bytes/1024**2:.2f} MB"
    else:
        return f"{size_bytes/1024**3:.2f} GB"

def send_lark_notification(stats, storage_type):
    """
    Sends a formatted notification to Lark.
    """
    if not LARK_WEBHOOK_URL or LARK_WEBHOOK_URL == "your_lark_webhook_url":
        print("Lark webhook URL is not configured. Skipping notification.")
        return

    if not stats or stats['total_files'] == 0:
        print("No stats to send or bucket is empty.")
        return

    latest_time_str = stats['latest_upload_time'].astimezone(timezone(timedelta(hours=8))).strftime('%Y-%m-%d %H:%M:%S') if stats['latest_upload_time'] else 'N/A'

    path = ""
    if storage_type == 's3':
        path = f"s3://{S3_BUCKET}/{S3_PREFIX}"
    elif storage_type == 'tos':
        path = f"tos://{TOS_BUCKET_NAME}/{TOS_PREFIX}"

    card_content = {
        "config": {
            "wide_screen_mode": True
        },
        "header": {
            "template": "blue",
            "title": {
                "content": f"{storage_type.upper()} 存储桶报告",
                "tag": "plain_text"
            }
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": f"**存储路径:** {path}",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": f"**总量统计**\n<font color='grey'>最近上传: {latest_time_str}</font>",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            },
            {
                "tag": "div",
                "fields": [
                    {
                        "is_short": True,
                        "text": {
                            "content": f"**文件总数**\n{stats['total_files']}",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": f"**总大小**\n{format_size(stats['total_size'])}",
                            "tag": "lark_md"
                        }
                    }
                ]
            },
            {
                "tag": "div",
                "text": {
                    "content": "**最近一小时报告**",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            },
            {
                "tag": "div",
                "fields": [
                    {
                        "is_short": True,
                        "text": {
                            "content": f"<font color='green'>**新增文件数**\n{stats['last_hour_files']}</font>",
                            "tag": "lark_md"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "content": f"<font color='green'>**新增大小**\n{format_size(stats['last_hour_size'])}</font>",
                            "tag": "lark_md"
                        }
                    }
                ]
            }
        ]
    }

    payload = {
        "msg_type": "interactive",
        "card": card_content
    }

    try:
        response = requests.post(LARK_WEBHOOK_URL, headers={'Content-Type': 'application/json'}, data=json.dumps(payload))
        response.raise_for_status()
        print("Lark notification sent successfully.")
    except requests.exceptions.RequestException as e:
        print(f"Error sending Lark notification: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send storage stats to Lark.")
    parser.add_argument('--storage', type=str, default='s3', choices=['s3', 'tos'],
                        help='The storage type to report on (s3 or tos). Defaults to s3.')
    args = parser.parse_args()

    print(f"Fetching stats for {args.storage.upper()}...")
    stats = None
    if args.storage == 's3':
        stats = get_s3_file_stats()
    elif args.storage == 'tos':
        stats = get_tos_file_stats()

    if stats:
        send_lark_notification(stats, args.storage)