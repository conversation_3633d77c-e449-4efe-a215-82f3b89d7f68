import os
import time
import boto3
import tos
from botocore.client import Config
from botocore.exceptions import ClientError
import concurrent.futures

# Import all necessary configurations from config.py
from config import (
    S3_ENDPOINT_URL,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    S3_BUCKET,
    S3_PREFIX,
    
    TOS_ENDPOINT,
    TOS_REGION,
    TOS_ACCESS_KEY,
    TOS_SECRET_KEY,
    TOS_BUCKET_NAME,
    TOS_PREFIX,

    BOTO_CONNECT_TIMEOUT,
    BOTO_READ_TIMEOUT,
    MAX_WORKERS,
    DATA_DIR
)

MIGRATED_VIDEOS_FILE = os.path.join(DATA_DIR, "s3_migration_tos_videos.txt")
TEMP_DOWNLOAD_DIR = os.path.join(DATA_DIR, "temp_migration_downloads")

def load_migrated_videos():
    """Loads the set of already migrated video IDs from the tracking file."""
    if not os.path.exists(MIGRATED_VIDEOS_FILE):
        return set()
    with open(MIGRATED_VIDEOS_FILE, "r", encoding='utf-8') as f:
        return {line.strip() for line in f if line.strip()}

def log_migrated_video(video_id):
    """Appends a video ID to the tracking file."""
    with open(MIGRATED_VIDEOS_FILE, "a", encoding='utf-8') as f:
        f.write(video_id + "\n")

def list_s3_objects():
    """Lists all objects in the S3 bucket with the configured prefix."""
    if not all([S3_ENDPOINT_URL, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, S3_BUCKET]):
        print("S3 credentials are not fully configured.")
        return []

    try:
        s3_client = boto3.client(
            's3',
            endpoint_url=S3_ENDPOINT_URL,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            config=Config(
                connect_timeout=BOTO_CONNECT_TIMEOUT,
                read_timeout=BOTO_READ_TIMEOUT,
                signature_version='s3v4'
            )
        )
        
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=S3_BUCKET, Prefix=S3_PREFIX)
        
        all_objects = []
        for page in pages:
            if "Contents" in page:
                all_objects.extend(page["Contents"])
        
        print(f"Found {len(all_objects)} objects in S3 bucket '{S3_BUCKET}' with prefix '{S3_PREFIX}'")
        return all_objects
        
    except ClientError as e:
        print(f"Failed to list S3 objects. Boto3 ClientError: {e}")
        return []
    except Exception as e:
        print(f"Failed to list S3 objects. An unexpected error occurred: {e}")
        return []

def download_from_s3(s3_client, object_key, local_path):
    """Downloads a file from S3."""
    try:
        s3_client.download_file(S3_BUCKET, object_key, local_path)
        return True
    except ClientError as e:
        print(f"Failed to download {object_key} from S3. Boto3 ClientError: {e}")
        return False
    except Exception as e:
        print(f"Failed to download {object_key} from S3. An unexpected error occurred: {e}")
        return False

def upload_to_tos(tos_client, local_path, tos_object_key):
    """Uploads a file to TOS."""
    try:
        start_time = time.time()
        result = tos_client.put_object_from_file(
            bucket=TOS_BUCKET_NAME,
            key=tos_object_key,
            file_path=local_path
        )
        duration = time.time() - start_time
        
        if result.status_code == 200:
            file_size_mb = os.path.getsize(local_path) / (1024 * 1024)
            print(f"Successfully uploaded to TOS: {tos_object_key} ({file_size_mb:.2f} MB) in {duration:.2f} seconds.")
            return True
        else:
            print(f"Failed to upload to TOS. Status: {result.status_code}, Request ID: {result.request_id}")
            return False
    except Exception as e:
        print(f"Failed to upload {local_path} to TOS. An unexpected error occurred: {e}")
        return False

def migrate_object(s3_object, migrated_ids, s3_client, tos_client):
    """Migrates a single object from S3 to TOS."""
    object_key = s3_object['Key']
    filename = os.path.basename(object_key)
    video_id = filename.split('_')[0]

    if video_id in migrated_ids:
        # print(f"Skipping already migrated video: {video_id}")
        return

    print(f"Starting migration for video: {video_id} (Object: {object_key})")

    local_path = os.path.join(TEMP_DOWNLOAD_DIR, filename)
    
    # 1. Download from S3
    print(f"Downloading {object_key} from S3...")
    if not download_from_s3(s3_client, object_key, local_path):
        return

    # 2. Upload to TOS
    tos_object_key = f"{TOS_PREFIX}{filename}"
    print(f"Uploading {filename} to TOS...")
    if not upload_to_tos(tos_client, local_path, tos_object_key):
        os.remove(local_path) # Clean up downloaded file on failure
        return

    # 3. Log success and clean up
    log_migrated_video(video_id)
    print(f"Successfully migrated {video_id}. Logged to {MIGRATED_VIDEOS_FILE}.")
    
    try:
        os.remove(local_path)
        # print(f"Cleaned up temporary file: {local_path}")
    except OSError as e:
        print(f"Error deleting temporary file {local_path}: {e}")


def main():
    """Main function to run the S3 to TOS migration."""
    print("--- S3 to TOS Migration Script ---")

    if not os.path.exists(TEMP_DOWNLOAD_DIR):
        os.makedirs(TEMP_DOWNLOAD_DIR)

    migrated_ids = load_migrated_videos()
    print(f"Loaded {len(migrated_ids)} already migrated video IDs.")

    s3_objects = list_s3_objects()
    if not s3_objects:
        print("No objects to migrate. Exiting.")
        return

    objects_to_migrate = [
        obj for obj in s3_objects 
        if os.path.basename(obj['Key']).split('_')[0] not in migrated_ids
    ]

    if not objects_to_migrate:
        print("All S3 objects have already been migrated. Exiting.")
        return
        
    print(f"Found {len(objects_to_migrate)} new objects to migrate.")

    # Initialize clients
    s3_client = boto3.client(
        's3',
        endpoint_url=S3_ENDPOINT_URL,
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        config=Config(signature_version='s3v4')
    )
    tos_client = tos.TosClientV2(
        ak=TOS_ACCESS_KEY,
        sk=TOS_SECRET_KEY,
        endpoint=TOS_ENDPOINT,
        region=TOS_REGION
    )

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = [
            executor.submit(migrate_object, obj, migrated_ids, s3_client, tos_client)
            for obj in objects_to_migrate
        ]
        
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"A migration task generated an exception: {e}")

    print("--- Migration script finished. ---")


if __name__ == "__main__":
    main()